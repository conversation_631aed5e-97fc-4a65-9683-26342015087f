<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.quran.test" >

    <uses-sdk
        android:minSdkVersion="23"
        android:targetSdkVersion="34" />

    <instrumentation
        android:name="android.test.InstrumentationTestRunner"
        android:functionalTest="false"
        android:handleProfiling="false"
        android:label="Tests for com.example.quran"
        android:targetPackage="com.example.quran" />

    <application
        android:debuggable="true"
        android:extractNativeLibs="false" >
        <uses-library android:name="android.test.runner" />
    </application>

</manifest>