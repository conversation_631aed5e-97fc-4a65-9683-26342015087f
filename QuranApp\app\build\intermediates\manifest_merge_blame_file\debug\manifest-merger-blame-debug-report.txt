1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.quran"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:4:22-64
12
13    <permission
13-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6a52a58d6388fcc8e0878e37edaaf2c\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
14        android:name="com.example.quran.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
14-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6a52a58d6388fcc8e0878e37edaaf2c\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
15        android:protectionLevel="signature" />
15-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6a52a58d6388fcc8e0878e37edaaf2c\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
16
17    <uses-permission android:name="com.example.quran.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
17-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6a52a58d6388fcc8e0878e37edaaf2c\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
17-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6a52a58d6388fcc8e0878e37edaaf2c\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
18
19    <application
19-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:6:5-24:19
20        android:allowBackup="true"
20-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:7:9-35
21        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
21-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e6a52a58d6388fcc8e0878e37edaaf2c\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:icon="@android:drawable/sym_def_app_icon"
24-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:8:9-58
25        android:label="Quran App"
25-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:9:9-34
26        android:roundIcon="@android:drawable/sym_def_app_icon"
26-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:10:9-63
27        android:supportsRtl="true"
27-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:11:9-35
28        android:theme="@style/Theme.Quran" >
28-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:12:9-43
29        <activity
29-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:13:9-20:20
30            android:name="com.example.quran.MainActivity"
30-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:14:13-41
31            android:exported="true" >
31-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:15:13-36
32            <intent-filter>
32-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:16:13-19:29
33                <action android:name="android.intent.action.MAIN" />
33-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:17:17-69
33-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:17:25-66
34
35                <category android:name="android.intent.category.LAUNCHER" />
35-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:18:17-77
35-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:18:27-74
36            </intent-filter>
37        </activity>
38        <activity
38-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:21:9-23:40
39            android:name="com.example.quran.SurahDetailActivity"
39-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:22:13-48
40            android:exported="false" />
40-->C:\Users\<USER>\Desktop\quran555\QuranApp\app\src\main\AndroidManifest.xml:23:13-37
41
42        <provider
42-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d91ca2b0256affa038bf49822d1807ef\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
43            android:name="androidx.startup.InitializationProvider"
43-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d91ca2b0256affa038bf49822d1807ef\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
44            android:authorities="com.example.quran.androidx-startup"
44-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d91ca2b0256affa038bf49822d1807ef\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
45            android:exported="false" >
45-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d91ca2b0256affa038bf49822d1807ef\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
46            <meta-data
46-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d91ca2b0256affa038bf49822d1807ef\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
47                android:name="androidx.emoji2.text.EmojiCompatInitializer"
47-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d91ca2b0256affa038bf49822d1807ef\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
48                android:value="androidx.startup" />
48-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d91ca2b0256affa038bf49822d1807ef\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
49            <meta-data
49-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c96a873f5d967efc2f1e2a52c3d9690\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
50-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c96a873f5d967efc2f1e2a52c3d9690\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
51                android:value="androidx.startup" />
51-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\2c96a873f5d967efc2f1e2a52c3d9690\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
53-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
54                android:value="androidx.startup" />
54-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
55        </provider>
56
57        <receiver
57-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
58            android:name="androidx.profileinstaller.ProfileInstallReceiver"
58-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
59            android:directBootAware="false"
59-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
60            android:enabled="true"
60-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
61            android:exported="true"
61-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
62            android:permission="android.permission.DUMP" >
62-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
63            <intent-filter>
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
64                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
65            </intent-filter>
66            <intent-filter>
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
67                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
67-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
68            </intent-filter>
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
70                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
70-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
73                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\2622e8acef8075d04a2bebc5b582a632\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
74            </intent-filter>
75        </receiver>
76    </application>
77
78</manifest>
