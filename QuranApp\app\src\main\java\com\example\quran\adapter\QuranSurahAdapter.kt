package com.example.quran.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.quran.R
import com.example.quran.model.Surah

class QuranSurahAdapter(
    private var surahList: List<Surah>,
    private val onItemClick: (Surah) -> Unit
) : RecyclerView.Adapter<QuranSurahAdapter.SurahViewHolder>() {

    inner class SurahViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val surahNameTextView: TextView = itemView.findViewById(R.id.surahNameTextView)
        val surahInfoTextView: TextView = itemView.findViewById(R.id.surahInfoTextView)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SurahViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_surah, parent, false)
        return SurahViewHolder(view)
    }

    override fun onBindViewHolder(holder: SurahViewHolder, position: Int) {
        val surah = surahList[position]
        holder.surahNameTextView.text = surah.name
        holder.surahInfoTextView.text = "عدد الآيات: ${surah.ayahs.size}"
        holder.itemView.setOnClickListener { onItemClick(surah) }
    }

    override fun getItemCount(): Int = surahList.size

    fun updateList(newList: List<Surah>) {
        surahList = newList
        notifyDataSetChanged()
    }
} 