<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns١="http://schemas.android.com/tools">
    <color name="black">#000000</color>
    <color name="purple_500">#6200EE</color>
    <color name="purple_700">#3700B3</color>
    <color name="teal_200">#03DAC5</color>
    <color name="teal_700">#018786</color>
    <color name="white">#FFFFFF</color>
    <string name="app_name">Quran App</string>
    <string name="ayah">آية</string>
    <string name="back">عودة</string>
    <string name="dark_mode">الوضع الليلي</string>
    <string name="light_mode">الوضع النهاري</string>
    <string name="play_audio">تشغيل التلاوة</string>
    <string name="search_hint">ابحث عن سورة...</string>
    <string name="show_tafsir">عرض التفسير</string>
    <string name="surah">سورة</string>
    <style name="Theme.Quran" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor" ns١:targetApi="l">@color/purple_700</item>
        <item name="android:windowLightStatusBar" ns١:targetApi="m">false</item>
    </style>
</resources>