<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/white">

    <TextView
        android:id="@+id/surahTitleTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="اسم السورة"
        android:textSize="22sp"
        android:textStyle="bold"
        android:textColor="@color/purple_700"
        android:layout_gravity="center_horizontal" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp">
        <TextView
            android:id="@+id/ayahsTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="نص الآيات"
            android:textSize="18sp"
            android:textColor="@color/black"
            android:gravity="right" />
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">
        <Button
            android:id="@+id/playAudioButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/play_audio"
            android:layout_marginEnd="16dp" />
        <Button
            android:id="@+id/showTafsirButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/show_tafsir" />
    </LinearLayout>

    <TextView
        android:id="@+id/tafsirTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="التفسير سيظهر هنا"
        android:textSize="16sp"
        android:textColor="@color/teal_700"
        android:visibility="gone"
        android:layout_marginTop="8dp" />

</LinearLayout> 