// Generated by view binder compiler. Do not edit!
package com.example.quran.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.quran.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySurahDetailBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView ayahsTextView;

  @NonNull
  public final Button playAudioButton;

  @NonNull
  public final Button showTafsirButton;

  @NonNull
  public final TextView surahTitleTextView;

  @NonNull
  public final TextView tafsirTextView;

  private ActivitySurahDetailBinding(@NonNull LinearLayout rootView,
      @NonNull TextView ayahsTextView, @NonNull Button playAudioButton,
      @NonNull Button showTafsirButton, @NonNull TextView surahTitleTextView,
      @NonNull TextView tafsirTextView) {
    this.rootView = rootView;
    this.ayahsTextView = ayahsTextView;
    this.playAudioButton = playAudioButton;
    this.showTafsirButton = showTafsirButton;
    this.surahTitleTextView = surahTitleTextView;
    this.tafsirTextView = tafsirTextView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySurahDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySurahDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_surah_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySurahDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ayahsTextView;
      TextView ayahsTextView = ViewBindings.findChildViewById(rootView, id);
      if (ayahsTextView == null) {
        break missingId;
      }

      id = R.id.playAudioButton;
      Button playAudioButton = ViewBindings.findChildViewById(rootView, id);
      if (playAudioButton == null) {
        break missingId;
      }

      id = R.id.showTafsirButton;
      Button showTafsirButton = ViewBindings.findChildViewById(rootView, id);
      if (showTafsirButton == null) {
        break missingId;
      }

      id = R.id.surahTitleTextView;
      TextView surahTitleTextView = ViewBindings.findChildViewById(rootView, id);
      if (surahTitleTextView == null) {
        break missingId;
      }

      id = R.id.tafsirTextView;
      TextView tafsirTextView = ViewBindings.findChildViewById(rootView, id);
      if (tafsirTextView == null) {
        break missingId;
      }

      return new ActivitySurahDetailBinding((LinearLayout) rootView, ayahsTextView, playAudioButton,
          showTafsirButton, surahTitleTextView, tafsirTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
