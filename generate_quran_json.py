import requests
import json

url = "https://api.alquran.cloud/v1/quran/quran-uthmani"
response = requests.get(url)
data = response.json()

surahs = []
for surah in data['data']['surahs']:
    index = surah['number']
    name = surah['name']  # الاسم العربي
    ayahs = [ayah['text'] for ayah in surah['ayahs']]
    audio = f"https://verses.quran.com/Abdul_Basit/Mujawwad/mp3/{str(index).zfill(3)}.mp3"
    tafsir = "يمكنك إضافة التفسير هنا"
    surahs.append({
        "index": index,
        "name": name,
        "ayahs": ayahs,
        "audio": audio,
        "tafsir": tafsir
    })

with open("quran_data.json", "w", encoding="utf-8") as f:
    json.dump(surahs, f, ensure_ascii=False, indent=2)

print("تم إنشاء ملف quran_data.json بنجاح!") 