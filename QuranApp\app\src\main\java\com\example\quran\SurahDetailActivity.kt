package com.example.quran

import android.media.MediaPlayer
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.example.quran.databinding.ActivitySurahDetailBinding
import com.example.quran.model.Surah
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.InputStreamReader

class SurahDetailActivity : AppCompatActivity() {
    private lateinit var binding: ActivitySurahDetailBinding
    private var mediaPlayer: MediaPlayer? = null
    private var tafsirVisible = false
    private var surah: Surah? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySurahDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val surahIndex = intent.getIntExtra("surah_index", 1)
        surah = loadSurahByIndex(surahIndex)
        surah?.let {
            binding.surahTitleTextView.text = it.name
            binding.ayahsTextView.text = it.ayahs.joinToString("\n\n")
            binding.tafsirTextView.text = it.tafsir
        }

        binding.playAudioButton.setOnClickListener {
            surah?.audio?.let { url ->
                playAudio(url)
            }
        }
        binding.showTafsirButton.setOnClickListener {
            tafsirVisible = !tafsirVisible
            binding.tafsirTextView.visibility = if (tafsirVisible) View.VISIBLE else View.GONE
        }
    }

    private fun playAudio(url: String) {
        mediaPlayer?.release()
        mediaPlayer = MediaPlayer().apply {
            setDataSource(url)
            prepare()
            start()
        }
    }

    private fun loadSurahByIndex(index: Int): Surah? {
        val inputStream = assets.open("quran_data.json")
        val reader = InputStreamReader(inputStream)
        val type = object : TypeToken<List<Surah>>() {}.type
        val surahs: List<Surah> = Gson().fromJson(reader, type)
        return surahs.find { it.index == index }
    }

    override fun onDestroy() {
        super.onDestroy()
        mediaPlayer?.release()
    }
} 