<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/surahNameTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="اسم السورة"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/purple_700" />

        <TextView
            android:id="@+id/surahInfoTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="عدد الآيات"
            android:textSize="14sp"
            android:textColor="@color/black" />
    </LinearLayout>

</androidx.cardview.widget.CardView> 