package com.example.quran

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.quran.adapter.QuranSurahAdapter
import com.example.quran.databinding.ActivityMainBinding
import com.example.quran.model.Surah
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.InputStreamReader

class MainActivity : AppCompatActivity() {
    private lateinit var binding: ActivityMainBinding
    private lateinit var adapter: QuranSurahAdapter
    private var surahList: List<Surah> = listOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        surahList = loadSurahsFromAssets()
        adapter = QuranSurahAdapter(surahList) { surah ->
            val intent = Intent(this, SurahDetailActivity::class.java)
            intent.putExtra("surah_index", surah.index)
            startActivity(intent)
        }
        binding.surahRecyclerView.layoutManager = LinearLayoutManager(this)
        binding.surahRecyclerView.adapter = adapter

        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean = false
            override fun onQueryTextChange(newText: String?): Boolean {
                val filtered = if (newText.isNullOrBlank()) surahList else surahList.filter {
                    it.name.contains(newText, ignoreCase = true)
                }
                adapter.updateList(filtered)
                return true
            }
        })
    }

    private fun loadSurahsFromAssets(): List<Surah> {
        val inputStream = assets.open("quran_data.json")
        val reader = InputStreamReader(inputStream)
        val type = object : TypeToken<List<Surah>>() {}.type
        return Gson().fromJson(reader, type)
    }
} 