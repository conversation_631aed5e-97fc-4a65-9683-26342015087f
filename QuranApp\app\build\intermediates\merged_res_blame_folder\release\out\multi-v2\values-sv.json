{"logs": [{"outputFile": "com.example.quran.app-mergeReleaseResources-34:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e6a52a58d6388fcc8e0878e37edaaf2c\\transformed\\core-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3443,3538,3640,3738,3837,3945,4050,9185", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3533,3635,3733,3832,3940,4045,4166,9281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ba0b40924081ff015842564bcc882e03\\transformed\\material-1.11.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1082,1174,1238,1298,1390,1455,1518,1580,1647,1711,1765,1870,1929,1990,2044,2113,2232,2315,2399,2535,2614,2698,2820,2906,2984,3038,3089,3155,3224,3298,3387,3463,3535,3612,3683,3757,3868,3959,4038,4125,4213,4285,4359,4444,4495,4574,4641,4722,4806,4868,4932,4995,5063,5170,5269,5368,5463,5521,5576", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "261,360,452,533,635,715,813,935,1014,1077,1169,1233,1293,1385,1450,1513,1575,1642,1706,1760,1865,1924,1985,2039,2108,2227,2310,2394,2530,2609,2693,2815,2901,2979,3033,3084,3150,3219,3293,3382,3458,3530,3607,3678,3752,3863,3954,4033,4120,4208,4280,4354,4439,4490,4569,4636,4717,4801,4863,4927,4990,5058,5165,5264,5363,5458,5516,5571,5649"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2989,3088,3180,3261,3363,4171,4269,4391,4470,4533,4625,4689,4749,4841,4906,4969,5031,5098,5162,5216,5321,5380,5441,5495,5564,5683,5766,5850,5986,6065,6149,6271,6357,6435,6489,6540,6606,6675,6749,6838,6914,6986,7063,7134,7208,7319,7410,7489,7576,7664,7736,7810,7895,7946,8025,8092,8173,8257,8319,8383,8446,8514,8621,8720,8819,8914,8972,9027", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,98,91,80,101,79,97,121,78,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,83,135,78,83,121,85,77,53,50,65,68,73,88,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77", "endOffsets": "311,3083,3175,3256,3358,3438,4264,4386,4465,4528,4620,4684,4744,4836,4901,4964,5026,5093,5157,5211,5316,5375,5436,5490,5559,5678,5761,5845,5981,6060,6144,6266,6352,6430,6484,6535,6601,6670,6744,6833,6909,6981,7058,7129,7203,7314,7405,7484,7571,7659,7731,7805,7890,7941,8020,8087,8168,8252,8314,8378,8441,8509,8616,8715,8814,8909,8967,9022,9100"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\100d4697d6d061d15d74aa133d42fef9\\transformed\\appcompat-1.6.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,873,966,1061,1156,1250,1352,1447,1544,1642,1738,1831,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,603,716,793,868,961,1056,1151,1245,1347,1442,1539,1637,1733,1826,1906,2012,2111,2207,2312,2415,2517,2671,2773,2853"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,419,522,633,717,819,932,1009,1084,1177,1272,1367,1461,1563,1658,1755,1853,1949,2042,2122,2228,2327,2423,2528,2631,2733,2887,9105", "endColumns": "102,102,110,83,101,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "414,517,628,712,814,927,1004,1079,1172,1267,1362,1456,1558,1653,1750,1848,1944,2037,2117,2223,2322,2418,2523,2626,2728,2882,2984,9180"}}]}]}