// Generated by view binder compiler. Do not edit!
package com.example.quran.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.quran.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSurahBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView surahInfoTextView;

  @NonNull
  public final TextView surahNameTextView;

  private ItemSurahBinding(@NonNull CardView rootView, @NonNull TextView surahInfoTextView,
      @NonNull TextView surahNameTextView) {
    this.rootView = rootView;
    this.surahInfoTextView = surahInfoTextView;
    this.surahNameTextView = surahNameTextView;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSurahBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSurahBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_surah, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSurahBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.surahInfoTextView;
      TextView surahInfoTextView = ViewBindings.findChildViewById(rootView, id);
      if (surahInfoTextView == null) {
        break missingId;
      }

      id = R.id.surahNameTextView;
      TextView surahNameTextView = ViewBindings.findChildViewById(rootView, id);
      if (surahNameTextView == null) {
        break missingId;
      }

      return new ItemSurahBinding((CardView) rootView, surahInfoTextView, surahNameTextView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
